from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse
from typing import List, Dict, Any
import logging
import json
import os
from aiortc import RTCPeerConnection, RTCSessionDescription
import sys
from pathlib import Path

# Add the backend directory to the path to import webrtc_stream
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

from webrtc_stream import RTSPVideoStreamTrack

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/augment", tags=["augment"])

# Store active peer connections for WebRTC streaming
pcs = set()

# Camera configuration path
WORKSPACE_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
CAMERA_JSON_PATH = os.path.join(WORKSPACE_ROOT, "backend", "data", "camera_configuration.json")

# RTSPVideoStreamTrack is now imported from webrtc_stream.py

@router.get("/decoders")
async def get_decoders() -> Dict[str, Any]:
    """
    Get available video decoders information.
    Returns a dictionary with decoder information.
    """
    # For now return a simple mock response with common decoders
    decoders = {
        "status": "success",
        "decoders": [
            {
                "name": "h264",
                "description": "H.264/AVC decoder",
                "supported": True,
                "hardware_acceleration": ["nvidia", "intel", "software"]
            },
            {
                "name": "h265",
                "description": "H.265/HEVC decoder",
                "supported": True,
                "hardware_acceleration": ["nvidia", "intel", "software"]
            },
            {
                "name": "vp8",
                "description": "VP8 decoder",
                "supported": True,
                "hardware_acceleration": ["software"]
            },
            {
                "name": "vp9",
                "description": "VP9 decoder",
                "supported": True,
                "hardware_acceleration": ["software"]
            }
        ]
    }
    return decoders

@router.post("/stream")
async def handle_webrtc_stream(request: Request):
    """
    Handle WebRTC streaming offer from client using camera configuration.
    Follows the standardized /api/augment/ endpoint pattern.
    """
    return await _handle_webrtc_request(request)

@router.post("/webrtc")
async def handle_webrtc_stream_alt(request: Request):
    """
    Handle WebRTC streaming offer from client using camera configuration.
    Alternative endpoint name for compatibility.
    """
    return await _handle_webrtc_request(request)

async def _handle_webrtc_request(request: Request):
    """
    Shared implementation for WebRTC streaming requests.
    """
    try:
        body = await request.json()
        offer = RTCSessionDescription(sdp=body["sdp"], type=body["type"])

        # Get camera info from request
        collection_name = body.get("collection_name")
        camera_ip = body.get("camera_ip")

        if not collection_name or not camera_ip:
            return JSONResponse({
                "success": False,
                "error": "collection_name and camera_ip are required"
            }, status_code=400)

        # Read camera configuration
        if not os.path.exists(CAMERA_JSON_PATH):
            return JSONResponse({
                "success": False,
                "error": "camera_configuration.json not found"
            }, status_code=404)

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)

        if collection_name not in camera_data:
            return JSONResponse({
                "success": False,
                "error": f"Collection '{collection_name}' not found"
            }, status_code=404)

        if camera_ip not in camera_data[collection_name]:
            return JSONResponse({
                "success": False,
                "error": f"Camera IP '{camera_ip}' not found in collection '{collection_name}'"
            }, status_code=404)

        # Get the RTSP URL for this camera
        rtsp_url = camera_data[collection_name][camera_ip]

        logger.info(f"Creating WebRTC connection for {collection_name}/{camera_ip} -> {rtsp_url}")

        pc = RTCPeerConnection()
        pcs.add(pc)

        video = RTSPVideoStreamTrack(rtsp_url)
        pc.addTrack(video)

        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)

        return JSONResponse({
            "success": True,
            "data": {
                "sdp": pc.localDescription.sdp,
                "type": pc.localDescription.type
            },
            "message": "WebRTC stream established successfully"
        })

    except Exception as e:
        logger.error(f"Error handling WebRTC stream: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)

# Direct WebRTC endpoint is handled in main.py