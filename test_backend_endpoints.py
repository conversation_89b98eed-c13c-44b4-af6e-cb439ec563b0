#!/usr/bin/env python3
"""
Test script to verify backend WebRTC endpoints are working correctly.
"""

import requests
import json
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_BASE = "http://localhost:8000"

def test_endpoint(endpoint, payload, description):
    """Test a specific endpoint with the given payload"""
    logger.info(f"Testing {description}...")
    logger.info(f"Endpoint: {endpoint}")
    logger.info(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(f"{API_BASE}{endpoint}", 
                               json=payload, 
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        logger.info(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("✅ SUCCESS")
            try:
                response_data = response.json()
                logger.info(f"Response: {json.dumps(response_data, indent=2)}")
            except:
                logger.info(f"Response: {response.text}")
        else:
            logger.error("❌ FAILED")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ ERROR: {str(e)}")
    
    logger.info("-" * 50)

def main():
    """Main test function"""
    logger.info("Testing Backend WebRTC Endpoints")
    logger.info("=" * 50)
    
    # Test payload with minimal WebRTC offer
    test_offer = {
        "sdp": "v=0\r\no=- 123456789 123456789 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 96\r\nc=IN IP4 127.0.0.1\r\na=rtcp:9 IN IP4 127.0.0.1\r\na=ice-ufrag:test\r\na=ice-pwd:test\r\na=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00\r\na=setup:actpass\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:96 VP8/90000\r\n",
        "type": "offer"
    }
    
    # Test 1: Collection-based WebRTC endpoint
    test_endpoint(
        "/api/augment/stream",
        {
            **test_offer,
            "collection_name": "k1",
            "camera_ip": "************"
        },
        "Collection-based WebRTC (/api/augment/stream)"
    )
    
    # Test 2: Alternative WebRTC endpoint
    test_endpoint(
        "/api/augment/webrtc",
        {
            **test_offer,
            "collection_name": "k1",
            "camera_ip": "************"
        },
        "Alternative WebRTC endpoint (/api/augment/webrtc)"
    )
    
    # Test 3: Direct WebRTC endpoint
    test_endpoint(
        "/api/direct-webrtc",
        {
            **test_offer,
            "rtsp_url": "rtsp://service:Krnl$001@************:554"
        },
        "Direct WebRTC endpoint (/api/direct-webrtc)"
    )
    
    # Test 4: Check camera configuration
    logger.info("Testing camera configuration access...")
    try:
        with open("backend/data/camera_configuration.json", "r") as f:
            config = json.load(f)
        logger.info("✅ Camera configuration loaded successfully")
        logger.info(f"Available collections: {list(config.keys())}")
        for collection, cameras in config.items():
            logger.info(f"Collection '{collection}': {list(cameras.keys())}")
    except Exception as e:
        logger.error(f"❌ Error loading camera configuration: {str(e)}")
    
    logger.info("=" * 50)
    logger.info("Backend endpoint testing completed!")

if __name__ == "__main__":
    main()
