
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import collectionsApi from '../services/collectionsApi';
import { generateCameraId, normalizeCollectionName, denormalizeCollectionName, parseCameraId } from '../utils/cameraUtils';
const { ipc<PERSON><PERSON>er } = window.require('electron');

const STORAGE_KEY = 'vms_cameras';
const LAYOUT_STORAGE_KEY = 'vms_layout';
const COLLECTIONS_STORAGE_KEY = 'vms_collections';
const BOOKMARKS_STORAGE_KEY = 'vms_bookmarks';

export const useCameraStore = create(
  persist(
    (set, get) => ({
      cameras: [],
      bookmarks: [],
      collections: [],
      activeCollection: null,
      currentLayout: 'grid',
      cameraJson: {},

      // Camera actions
      setCameras: (cameras) => set({ cameras }),
      addCamera: (name, streamUrl, collectionId = null, analyticUrl = '') => {
        const newCamera = {
          id: `camera-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          name,
          streamUrl,
          analyticUrl,
          position: get().cameras.length,
          collectionId
        };

        set((state) => {
          const updatedCameras = [...state.cameras, newCamera];
          let updatedCollections = state.collections;

          if (collectionId) {
            updatedCollections = state.collections.map(collection => {
              if (collection.id === collectionId) {
                return {
                  ...collection,
                  cameras: [...collection.cameras, newCamera.id]
                };
              }
              return collection;
            });
          }

          return {
            cameras: updatedCameras,
            collections: updatedCollections,
            activeCollection: collectionId
          };
        });

        return newCamera.id;
      },

      // Helper function to check if an IP exists in any collection
      isIpExists: (ip) => {
        const state = get();
        return Object.values(state.cameraJson).some(collection =>
          Object.hasOwn(collection, ip)
        );
      },

      // Update camera configuration
      updateCameraConfig: async (collectionName, ip, streamUrl) => {
        try {
          console.log(`Updating camera configuration for ${ip} in collection ${collectionName}`);

          // Check if the collection exists in the backend
          let collectionExists = false;
          try {
            await collectionsApi.getCollection(collectionName);
            collectionExists = true;
          } catch (error) {
            if (error.response && error.response.status === 404) {
              console.log(`Collection ${collectionName} does not exist, creating it`);
              await collectionsApi.createCollection(collectionName);
            } else {
              throw error;
            }
          }

          // Add or update the camera in the collection
          if (collectionExists) {
            try {
              // Try to update the camera first
              await collectionsApi.updateCameraInCollection(collectionName, ip, streamUrl);
              console.log(`Updated camera ${ip} in collection ${collectionName}`);
            } catch (error) {
              if (error.response && error.response.status === 404) {
                // Camera doesn't exist, add it
                await collectionsApi.addCameraToCollection(collectionName, ip, streamUrl);
                console.log(`Added camera ${ip} to collection ${collectionName}`);
              } else {
                throw error;
              }
            }
          } else {
            // Collection was just created, add the camera
            await collectionsApi.addCameraToCollection(collectionName, ip, streamUrl);
            console.log(`Added camera ${ip} to new collection ${collectionName}`);
          }

          // Update the local state
          set((state) => {
            // Create a deep copy of the existing configuration
            const updatedJson = JSON.parse(JSON.stringify(state.cameraJson));

            // Initialize collection if not exists
            if (!updatedJson[collectionName]) {
              updatedJson[collectionName] = {};
            }

            // Add the new camera while preserving existing ones
            updatedJson[collectionName] = {
              ...updatedJson[collectionName],
              [ip]: streamUrl
            };

            // Find the collection
            const collection = state.collections.find(c => c.name === collectionName);
            if (!collection) return { cameraJson: updatedJson };

            // Create or update camera in the cameras array
            const cameraId = generateCameraId(collectionName, ip);
            const existingCamera = state.cameras.find(c => c.id === cameraId);

            let updatedCameras = [...state.cameras];
            if (!existingCamera) {
              updatedCameras.push({
                id: cameraId,
                name: `${collectionName} (${ip})`,
                streamUrl,
                position: state.cameras.length,
                collectionId: collection.id
              });
            }

            // Update collection's cameras array
            const updatedCollections = state.collections.map(c => {
              if (c.id === collection.id) {
                return {
                  ...c,
                  cameras: [...new Set([...c.cameras, cameraId])]
                };
              }
              return c;
            });

            return {
              cameraJson: updatedJson,
              cameras: updatedCameras,
              collections: updatedCollections
            };
          });

          // Reload the camera configuration to ensure consistency
          get().loadCameraConfig();
        } catch (error) {
          console.error(`Error updating camera configuration:`, error);
          throw new Error(`Failed to update camera configuration: ${error.message}`);
        }
      },

      // Save camera configuration to file
      saveCameraConfig: () => {
        const state = get();
        const configToSave = JSON.stringify(state.cameraJson, null, 2);
        console.log('Saving camera config to file. Collections:', Object.keys(state.cameraJson));
        console.log('Config data:', configToSave);

        // Add a timestamp to help track when the save operation occurs
        console.log(`Saving camera configuration at ${new Date().toISOString()}`);

        // Send the configuration to the main process
        ipcRenderer.send('save-camera-config', configToSave);

        // Add a listener for the save response if it doesn't exist
        if (!window._saveConfigListenerAdded) {
          ipcRenderer.once('save-camera-config-reply', (_, response) => {
            if (response.success) {
              console.log('Camera configuration saved successfully');
            } else {
              console.error('Error saving camera configuration:', response.error);
            }
          });
          window._saveConfigListenerAdded = true;
        }
      },

      // Load camera configuration from backend API
      loadCameraConfig: async () => {
        try {
          console.log('Loading camera configuration from backend API');

          // Get all collections from the backend
          const collections = await collectionsApi.getCollections();
          console.log('Collections from backend:', collections);

          // Initialize an empty configuration object
          const config = {};

          // For each collection, get its cameras
          for (const collectionName of collections) {
            try {
              const collectionData = await collectionsApi.getCollection(collectionName);
              config[collectionName] = collectionData.cameras;
            } catch (error) {
              console.error(`Error loading cameras for collection ${collectionName}:`, error);
            }
          }

          console.log('Loaded camera configuration:', config);

          // Initialize the camera configuration with the loaded data
          get().initializeCameraConfig(config);
        } catch (error) {
          console.error('Error loading camera configuration from backend:', error);
        }
      },

      // Initialize camera configuration
      initializeCameraConfig: (config) => {
        console.log('Initializing camera configuration with:', config);
        console.log('Collections in config:', Object.keys(config));

        set((state) => {
          // Update cameraJson with the new config
          const updatedCameraJson = config;

          // Create a map of IP addresses to their collection names
          const ipToCollection = {};
          Object.entries(updatedCameraJson).forEach(([collectionName, cameras]) => {
            Object.keys(cameras).forEach(ip => {
              ipToCollection[ip] = collectionName;
            });
          });
          console.log('IP to collection mapping created:', ipToCollection);

          // Update cameras array to match the configuration
          const updatedCameras = [];
          Object.entries(updatedCameraJson).forEach(([collectionName, cameras]) => {
            const collection = state.collections.find(c => c.name === collectionName);
            if (collection) {
              console.log(`Processing cameras for collection: ${collectionName}`);
              Object.entries(cameras).forEach(([ip, streamUrl]) => {
                const cameraName = `${collectionName} (${ip})`;
                const cameraId = generateCameraId(collectionName, ip);

                // Check if camera already exists
                const existingCamera = state.cameras.find(c => c.id === cameraId);
                if (!existingCamera) {
                  updatedCameras.push({
                    id: cameraId,
                    name: cameraName,
                    streamUrl,
                    position: updatedCameras.length,
                    collectionId: collection.id
                  });
                }
              });
            } else {
              console.log(`Creating new collection: ${collectionName}`);
              // Create the collection if it doesn't exist
              const newCollectionId = `collection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
              state.collections.push({
                id: newCollectionId,
                name: collectionName,
                cameras: []
              });
            }
          });
          console.log(`Updated cameras array with ${updatedCameras.length} cameras`);

          // Update collections to only include cameras from the configuration
          const updatedCollections = state.collections.map(collection => ({
            ...collection,
            cameras: updatedCameras
              .filter(camera => camera.collectionId === collection.id)
              .map(camera => camera.id)
          }));
          console.log(`Updated collections:`, updatedCollections);

          return {
            cameraJson: updatedCameraJson,
            cameras: updatedCameras,
            collections: updatedCollections
          };
        });
      },

      updateCamera: (cameraId, updates) =>
        set((state) => {
          // First, find the camera to update
          const camera = state.cameras.find(c => c.id === cameraId);
          if (!camera) {
            console.error(`Camera with ID ${cameraId} not found`);
            return state;
          }

          // Find the collection this camera belongs to
          const collection = state.collections.find(c => c.id === camera.collectionId);
          if (!collection) {
            console.error(`Collection for camera ${cameraId} not found`);
            return state;
          }

          // Parse the camera ID to extract the IP
          const { collectionName, ip } = parseCameraId(cameraId, state.collections);

          if (!ip) {
            console.error(`Could not extract IP from camera ID: ${cameraId}`);
            return state;
          }

          console.log(`Extracted IP from camera ID: ${ip}`);

          // Update the camera in the backend
          if (updates.streamUrl && updates.streamUrl !== camera.streamUrl) {
            console.log(`Updating camera ${ip} in collection ${collection.name} with new URL: ${updates.streamUrl}`);
            // Use the collectionsApi to update the camera in the backend
            collectionsApi.updateCameraInCollection(collection.name, ip, updates.streamUrl)
              .then(response => {
                console.log('Camera updated in backend:', response);
              })
              .catch(error => {
                console.error('Error updating camera in backend:', error);
              });
          }

          // Update the camera in the frontend state
          return {
            cameras: state.cameras.map(c =>
              c.id === cameraId ? { ...c, ...updates } : c
            )
          };
        }),
      removeCamera: (cameraId) =>
        set((state) => ({
          cameras: state.cameras.filter(camera => camera.id !== cameraId),
          bookmarks: state.bookmarks.filter(id => id !== cameraId),
          collections: state.collections.map(collection => ({
            ...collection,
            cameras: collection.cameras.filter(id => id !== cameraId)
          }))
        })),
      updateCameraPosition: (dragIndex, hoverIndex) => {
        const cameras = get().cameras;
        const draggedCamera = cameras[dragIndex];
        const updatedCameras = [...cameras];
        updatedCameras.splice(dragIndex, 1);
        updatedCameras.splice(hoverIndex, 0, draggedCamera);

        // Update positions
        updatedCameras.forEach((camera, index) => {
          camera.position = index;
        });

        set({ cameras: updatedCameras });
      },

      // Bookmark actions
      toggleBookmark: (cameraId) =>
        set((state) => {
          const isBookmarked = state.bookmarks.includes(cameraId);
          const newBookmarks = isBookmarked
            ? state.bookmarks.filter(id => id !== cameraId)
            : [...state.bookmarks, cameraId];

          console.log('Toggling bookmark for camera:', cameraId);
          console.log('Current bookmarks:', state.bookmarks);
          console.log('New bookmarks:', newBookmarks);
          console.log('Camera exists in store:', state.cameras.some(cam => cam.id === cameraId));

          return {
            bookmarks: newBookmarks
          };
        }),
      isBookmarked: (cameraId) => {
        const result = get().bookmarks.includes(cameraId);
        console.log('Checking if bookmarked:', cameraId, result);
        return result;
      },
      getBookmarkedCameras: () => {
        const bookmarks = get().bookmarks;
        const cameras = get().cameras;
        console.log('Getting bookmarked cameras. Total bookmarks:', bookmarks.length);
        console.log('Total cameras in store:', cameras.length);

        const bookmarkedCameras = cameras.filter(camera => bookmarks.includes(camera.id));
        console.log('Found bookmarked cameras:', bookmarkedCameras.length);
        return bookmarkedCameras;
      },

      // Collection actions
      setCollections: (collections) => set({ collections }),
      createCollection: async (name) => {
        try {
          console.log(`Creating collection with name: ${name}`);

          // Create a new collection ID
          const newCollectionId = `collection-${Date.now()}`;

          // Create the collection in the backend
          await collectionsApi.createCollection(name);

          // Update the local state
          const newCollection = {
            id: newCollectionId,
            name,
            cameras: []
          };

          set((state) => ({
            collections: [...state.collections, newCollection]
          }));

          // Reload the camera configuration to ensure consistency
          get().loadCameraConfig();

          return newCollectionId;
        } catch (error) {
          console.error(`Error creating collection ${name}:`, error);
          throw new Error(`Failed to create collection: ${error.message}`);
        }
      },
      renameCollection: async (collectionId, newName) => {
        try {
          console.log(`Renaming collection with ID: ${collectionId} to ${newName}`);

          // Find the collection in the state
          const collections = get().collections;
          const collection = collections.find(col => col.id === collectionId);

          if (!collection) {
            throw new Error(`Collection with ID ${collectionId} not found`);
          }

          // Check if a collection with the new name already exists
          const existingCollection = collections.find(
            c => c.id !== collectionId && c.name.toLowerCase() === newName.trim().toLowerCase()
          );

          if (existingCollection) {
            throw new Error('A collection with this name already exists');
          }

          // Update the collection in the backend
          await collectionsApi.updateCollection(collection.name, newName.trim());

          // Update the local state
          set((state) => ({
            collections: state.collections.map(col =>
              col.id === collectionId
                ? { ...col, name: newName.trim() }
                : col
            )
          }));

          // Reload the camera configuration to ensure consistency
          get().loadCameraConfig();
        } catch (error) {
          console.error(`Error renaming collection ${collectionId}:`, error);
          throw new Error(`Failed to rename collection: ${error.message}`);
        }
      },
      deleteCollection: async (collectionId) => {
        try {
          console.log(`Deleting collection with ID: ${collectionId}`);

          // Find the collection in the state
          const state = get();
          const collection = state.collections.find(col => col.id === collectionId);

          if (!collection) {
            console.error(`Collection with ID ${collectionId} not found in state`);
            return;
          }

          console.log(`Found collection to delete: ${collection.name} (${collectionId})`);

          // Delete the collection from the backend
          await collectionsApi.deleteCollection(collection.name);

          // Update the local state
          set((state) => {
            // Create a deep copy of the cameraJson to avoid direct state mutation
            let updatedCameraJson = JSON.parse(JSON.stringify(state.cameraJson));
            console.log(`Current collections in cameraJson:`, Object.keys(updatedCameraJson));

            if (updatedCameraJson[collection.name]) {
              console.log(`Removing collection '${collection.name}' from cameraJson`);
              // Remove the collection from cameraJson
              const { [collection.name]: _, ...rest } = updatedCameraJson;
              updatedCameraJson = rest;
              console.log(`Collections after removal:`, Object.keys(updatedCameraJson));
            } else {
              console.log(`Collection '${collection.name}' not found in cameraJson`);
            }

            const camerasInCollection = state.cameras.filter(camera => camera.collectionId === collectionId);
            console.log(`Removing ${camerasInCollection.length} cameras from the collection`);

            const updatedCameras = state.cameras.filter(camera => camera.collectionId !== collectionId);
            const updatedCollections = state.collections.filter(collection => collection.id !== collectionId);

            return {
              cameras: updatedCameras,
              collections: updatedCollections,
              activeCollection: state.activeCollection === collectionId ? null : state.activeCollection,
              cameraJson: updatedCameraJson
            };
          });

          // Reload the camera configuration to ensure consistency
          get().loadCameraConfig();
        } catch (error) {
          console.error('Unexpected error in deleteCollection:', error);
          throw new Error(`Failed to delete collection: ${error.message}`);
        }
      },
      addCameraToCollection: (cameraId, collectionId) => {
        set((state) => ({
          collections: state.collections.map(collection => {
            if (collection.id === collectionId) {
              return {
                ...collection,
                cameras: [...collection.cameras, cameraId]
              };
            }
            return collection;
          }),
          cameras: state.cameras.map(camera => {
            if (camera.id === cameraId) {
              return {
                ...camera,
                collectionId
              };
            }
            return camera;
          })
        }));
      },
      removeCameraFromCollection: async (cameraId, collectionId) => {
        try {
          // Find the camera and collection in the state
          const state = get();
          const camera = state.cameras.find(cam => cam.id === cameraId);
          const collection = state.collections.find(col => col.id === collectionId);

          if (!camera || !collection) {
            console.error(`Camera or collection not found: camera=${cameraId}, collection=${collectionId}`);
            return;
          }

          console.log(`Removing camera ${cameraId} from collection ${collection.name}`);

          // Extract IP from camera ID using the parseCameraId utility
          let ipToRemove = null;

          // Use the parseCameraId utility function for consistent parsing
          const { collectionName: parsedCollection, ip: parsedIp } = parseCameraId(camera.id, state.collections);
          if (parsedIp) {
            ipToRemove = parsedIp;
            console.log(`Extracted IP from camera ID using parseCameraId: ${ipToRemove}`);
          }

          // If that fails, try to find by matching stream URL in the cameraJson
          if (!ipToRemove && state.cameraJson[collection.name]) {
            console.log(`Trying to find IP by matching stream URL`);
            ipToRemove = Object.keys(state.cameraJson[collection.name]).find(ip => {
              return state.cameraJson[collection.name][ip] === camera.streamUrl;
            });
            console.log(`Found IP by stream URL: ${ipToRemove}`);
          }

          // If we found the IP, remove it from the collection in the backend
          if (ipToRemove) {
            console.log(`Removing IP ${ipToRemove} from collection ${collection.name} in backend`);
            await collectionsApi.removeCameraFromCollection(collection.name, ipToRemove);
          } else {
            console.warn(`Could not find IP for camera ${cameraId} in collection ${collection.name}`);
          }

          // Update the local state
          set((state) => {
            // Create a deep copy of the cameraJson to avoid direct state mutation
            let updatedCameraJson = JSON.parse(JSON.stringify(state.cameraJson));

            if (updatedCameraJson[collection.name] && ipToRemove) {
              // Create a new object without the camera to be removed
              const updatedCollectionCameras = {};
              Object.keys(updatedCameraJson[collection.name]).forEach(ip => {
                if (ip !== ipToRemove) {
                  updatedCollectionCameras[ip] = updatedCameraJson[collection.name][ip];
                }
              });

              // Update the collection in cameraJson
              updatedCameraJson[collection.name] = updatedCollectionCameras;

              console.log(`Updated collection cameras:`, Object.keys(updatedCameraJson[collection.name]));
            }

            // Update the collections and cameras arrays
            return {
              collections: state.collections.map(col => {
                if (col.id === collectionId) {
                  return {
                    ...col,
                    cameras: col.cameras.filter(id => id !== cameraId)
                  };
                }
                return col;
              }),
              cameras: state.cameras.map(cam => {
                if (cam.id === cameraId) {
                  return {
                    ...cam,
                    collectionId: null
                  };
                }
                return cam;
              }),
              cameraJson: updatedCameraJson
            };
          });

          // Reload the camera configuration to ensure consistency
          get().loadCameraConfig();
        } catch (error) {
          console.error(`Error removing camera from collection:`, error);
          throw new Error(`Failed to remove camera from collection: ${error.message}`);
        }
      },
      setCollectionActive: (collectionId) => set({ activeCollection: collectionId }),
      getCamerasByCollection: (collectionId) =>
        get().cameras.filter(camera => camera.collectionId === collectionId),

      // Layout actions
      updateLayout: (layout) => set({ currentLayout: layout })
    }),
    {
      name: 'camera-storage',
      getStorage: () => localStorage,
      partialize: (state) => ({
        cameras: state.cameras,
        bookmarks: state.bookmarks,
        collections: state.collections,
        currentLayout: state.currentLayout
      })
    }
  )
);
