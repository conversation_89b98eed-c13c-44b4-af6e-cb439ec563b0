<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera ************ Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .camera-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            padding: 12px 24px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .start-btn {
            background: #4caf50;
            color: white;
        }
        .stop-btn {
            background: #f44336;
            color: white;
        }
        .test-btn {
            background: #2196f3;
            color: white;
        }
        .video-container {
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            min-height: 400px;
            position: relative;
            margin-bottom: 20px;
        }
        video {
            width: 100%;
            height: 400px;
            object-fit: contain;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin-bottom: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #ef5350;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #4caf50;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 8px;
        }
        .status.idle { background: #9e9e9e; color: white; }
        .status.connecting { background: #ff9800; color: white; }
        .status.live { background: #4caf50; color: white; }
        .status.error { background: #f44336; color: white; }
        .instructions {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Camera ************ Frontend Test</h1>
        
        <div class="camera-info">
            <h3>Camera Information</h3>
            <p><strong>IP Address:</strong> ************</p>
            <p><strong>RTSP URL:</strong> rtsp://service:Krnl$001@************:554</p>
            <p><strong>Collection:</strong> k1</p>
            <p><strong>Status:</strong> <span id="status" class="status idle">Idle</span></p>
        </div>

        <div class="controls">
            <button id="testWebRTC" class="test-btn">Test WebRTC Player</button>
            <button id="testDirect" class="test-btn">Test Direct WebRTC</button>
            <button id="stopStream" class="stop-btn" style="display: none;">Stop Stream</button>
        </div>

        <div id="errorContainer" style="display: none;"></div>
        <div id="successContainer" style="display: none;"></div>

        <div class="video-container">
            <video id="videoElement" autoplay playsinline controls style="display: none;"></video>
            <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                <div class="loading-spinner"></div>
                <div>Connecting to stream...</div>
            </div>
            <div id="placeholderText" style="display: flex; align-items: center; justify-content: center; height: 400px; color: #666; font-size: 18px;">
                Click a test button to start streaming camera ************
            </div>
        </div>

        <div class="instructions">
            <h3>Test Instructions</h3>
            <ol>
                <li><strong>Test WebRTC Player:</strong> Uses the collection-based API endpoint (/api/augment/webrtc)</li>
                <li><strong>Test Direct WebRTC:</strong> Uses the direct RTSP URL endpoint (/api/direct-webrtc)</li>
                <li>Make sure your backend server is running on port 8000</li>
                <li>The video should appear with minimal latency</li>
                <li>Check the browser console for additional debug information</li>
            </ol>
            
            <h4>Expected Results:</h4>
            <ul>
                <li>Status should change: Idle → Connecting → Live</li>
                <li>Video stream should display smoothly at 1920x1080 @ 30 FPS</li>
                <li>No error messages should appear</li>
                <li>Both test modes should work successfully</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        const videoElement = document.getElementById('videoElement');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const placeholderText = document.getElementById('placeholderText');
        const statusElement = document.getElementById('status');
        const errorContainer = document.getElementById('errorContainer');
        const successContainer = document.getElementById('successContainer');
        const stopButton = document.getElementById('stopStream');
        
        let currentPeerConnection = null;
        let currentTestMode = null;

        // Camera configuration
        const cameraConfig = {
            ip: '************',
            rtspUrl: 'rtsp://service:Krnl$001@************:554',
            collectionName: 'k1'
        };

        function updateStatus(status) {
            statusElement.className = `status ${status}`;
            statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }

        function showError(message) {
            errorContainer.innerHTML = `<div class="error"><strong>Error:</strong> ${message}</div>`;
            errorContainer.style.display = 'block';
            successContainer.style.display = 'none';
        }

        function showSuccess(message) {
            successContainer.innerHTML = `<div class="success"><strong>Success:</strong> ${message}</div>`;
            successContainer.style.display = 'block';
            errorContainer.style.display = 'none';
        }

        function clearMessages() {
            errorContainer.style.display = 'none';
            successContainer.style.display = 'none';
        }

        function showLoading() {
            videoElement.style.display = 'none';
            placeholderText.style.display = 'none';
            loadingOverlay.style.display = 'flex';
            updateStatus('connecting');
        }

        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }

        function showVideo() {
            hideLoading();
            placeholderText.style.display = 'none';
            videoElement.style.display = 'block';
            stopButton.style.display = 'inline-block';
            updateStatus('live');
        }

        function resetUI() {
            hideLoading();
            videoElement.style.display = 'none';
            videoElement.srcObject = null;
            placeholderText.style.display = 'flex';
            stopButton.style.display = 'none';
            updateStatus('idle');
        }

        async function testWebRTCPlayer() {
            try {
                currentTestMode = 'webrtc';
                clearMessages();
                showLoading();
                
                console.log('Testing WebRTC Player with collection-based API...');
                
                const pc = new RTCPeerConnection();
                currentPeerConnection = pc;

                pc.ontrack = function(event) {
                    console.log('Received video track');
                    videoElement.srcObject = event.streams[0];
                    showVideo();
                    showSuccess('WebRTC Player connected successfully!');
                };

                pc.oniceconnectionstatechange = function() {
                    console.log('ICE connection state:', pc.iceConnectionState);
                    if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
                        showError('WebRTC connection failed');
                        updateStatus('error');
                    }
                };

                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);

                const response = await fetch(`${API_BASE}/api/augment/webrtc`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: offer.sdp,
                        type: offer.type,
                        collection_name: cameraConfig.collectionName,
                        camera_ip: cameraConfig.ip
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const answer = await response.json();
                await pc.setRemoteDescription(new RTCSessionDescription(answer));
                
            } catch (error) {
                console.error('WebRTC Player test failed:', error);
                showError(`WebRTC Player test failed: ${error.message}`);
                updateStatus('error');
                resetUI();
            }
        }

        async function testDirectWebRTC() {
            try {
                currentTestMode = 'direct';
                clearMessages();
                showLoading();
                
                console.log('Testing Direct WebRTC with RTSP URL...');
                
                const pc = new RTCPeerConnection();
                currentPeerConnection = pc;

                pc.ontrack = function(event) {
                    console.log('Received video track');
                    videoElement.srcObject = event.streams[0];
                    showVideo();
                    showSuccess('Direct WebRTC connected successfully!');
                };

                pc.oniceconnectionstatechange = function() {
                    console.log('ICE connection state:', pc.iceConnectionState);
                    if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
                        showError('WebRTC connection failed');
                        updateStatus('error');
                    }
                };

                const offer = await pc.createOffer();
                await pc.setLocalDescription(offer);

                const response = await fetch(`${API_BASE}/api/direct-webrtc`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: offer.sdp,
                        type: offer.type,
                        rtsp_url: cameraConfig.rtspUrl
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const answer = await response.json();
                await pc.setRemoteDescription(new RTCSessionDescription(answer));
                
            } catch (error) {
                console.error('Direct WebRTC test failed:', error);
                showError(`Direct WebRTC test failed: ${error.message}`);
                updateStatus('error');
                resetUI();
            }
        }

        function stopStream() {
            if (currentPeerConnection) {
                currentPeerConnection.close();
                currentPeerConnection = null;
            }
            resetUI();
            clearMessages();
            console.log('Stream stopped');
        }

        // Event listeners
        document.getElementById('testWebRTC').addEventListener('click', testWebRTCPlayer);
        document.getElementById('testDirect').addEventListener('click', testDirectWebRTC);
        document.getElementById('stopStream').addEventListener('click', stopStream);

        // Video event listeners
        videoElement.addEventListener('loadeddata', () => {
            console.log('Video loaded successfully');
        });

        videoElement.addEventListener('error', (e) => {
            console.error('Video error:', e);
            showError('Video playback error');
        });

        console.log('Camera ************ test page loaded');
        console.log('Camera config:', cameraConfig);
    </script>
</body>
</html>
