import React, { useState } from 'react';
import WebRTCPlayer from './WebRTCPlayer';
import DirectWebRTCPlayer from './DirectWebRTCPlayer';

const Camera172Test = () => {
  const [testMode, setTestMode] = useState('webrtc'); // 'webrtc' or 'direct'
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState(null);
  const [streamStatus, setStreamStatus] = useState('idle');

  // Camera configuration for IP ************
  const cameraConfig = {
    ip: '************',  // Updated to match your test
    rtspUrl: 'rtsp://service:Krnl$001@************:554',
    collectionName: 'k1' // Based on your camera_configuration.json
  };

  const handleStartStream = () => {
    setError(null);
    setIsStreaming(true);
    setStreamStatus('connecting');
  };

  const handleStopStream = () => {
    setIsStreaming(false);
    setStreamStatus('idle');
    setError(null);
  };

  const handleError = (errorMessage) => {
    setError(errorMessage);
    setStreamStatus('error');
    console.error('Camera stream error:', errorMessage);
  };

  const handlePlay = () => {
    setStreamStatus('playing');
    console.log('Camera stream started successfully');
  };

  const getStatusColor = () => {
    switch (streamStatus) {
      case 'connecting': return '#ff9800';
      case 'playing': return '#4caf50';
      case 'error': return '#f44336';
      default: return '#9e9e9e';
    }
  };

  const getStatusText = () => {
    switch (streamStatus) {
      case 'connecting': return 'Connecting...';
      case 'playing': return 'Live';
      case 'error': return 'Error';
      default: return 'Idle';
    }
  };

  return (
    <div style={{
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>Camera ************ Test</h1>

      {/* Camera Info */}
      <div style={{
        backgroundColor: '#f5f5f5',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Camera Information</h3>
        <p><strong>IP Address:</strong> {cameraConfig.ip}</p>
        <p><strong>RTSP URL:</strong> {cameraConfig.rtspUrl}</p>
        <p><strong>Collection:</strong> {cameraConfig.collectionName}</p>
        <p>
          <strong>Status:</strong>
          <span style={{
            color: getStatusColor(),
            fontWeight: 'bold',
            marginLeft: '8px'
          }}>
            {getStatusText()}
          </span>
        </p>
      </div>

      {/* Test Mode Selection */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Test Mode</h3>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button
            onClick={() => setTestMode('webrtc')}
            style={{
              padding: '10px 20px',
              backgroundColor: testMode === 'webrtc' ? '#2196f3' : '#e0e0e0',
              color: testMode === 'webrtc' ? 'white' : 'black',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            WebRTC Player (Collection-based)
          </button>
          <button
            onClick={() => setTestMode('direct')}
            style={{
              padding: '10px 20px',
              backgroundColor: testMode === 'direct' ? '#2196f3' : '#e0e0e0',
              color: testMode === 'direct' ? 'white' : 'black',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Direct WebRTC Player (RTSP URL)
          </button>
        </div>
      </div>

      {/* Controls */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={isStreaming ? handleStopStream : handleStartStream}
          style={{
            padding: '12px 24px',
            backgroundColor: isStreaming ? '#f44336' : '#4caf50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '16px',
            marginRight: '10px'
          }}
        >
          {isStreaming ? 'Stop Stream' : 'Start Stream'}
        </button>

        {error && (
          <button
            onClick={() => {
              setError(null);
              setStreamStatus('idle');
            }}
            style={{
              padding: '12px 24px',
              backgroundColor: '#ff9800',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Clear Error
          </button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div style={{
          backgroundColor: '#ffebee',
          color: '#c62828',
          padding: '15px',
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #ef5350'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Video Stream Container */}
      <div style={{
        backgroundColor: '#000',
        borderRadius: '8px',
        overflow: 'hidden',
        minHeight: '400px',
        position: 'relative'
      }}>
        {isStreaming ? (
          testMode === 'webrtc' ? (
            <WebRTCPlayer
              collectionName={cameraConfig.collectionName}
              cameraIp={cameraConfig.ip}
              onError={handleError}
              onPlay={handlePlay}
            />
          ) : (
            <DirectWebRTCPlayer
              rtspUrl={cameraConfig.rtspUrl}
              onError={handleError}
              onPlay={handlePlay}
            />
          )
        ) : (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '400px',
            color: '#666',
            fontSize: '18px'
          }}>
            Click "Start Stream" to begin testing camera ************
          </div>
        )}
      </div>

      {/* Instructions */}
      <div style={{ marginTop: '30px' }}>
        <h3>Test Instructions</h3>
        <ol style={{ lineHeight: '1.6' }}>
          <li>Select a test mode:
            <ul>
              <li><strong>WebRTC Player:</strong> Uses the collection-based API endpoint (/api/augment/webrtc)</li>
              <li><strong>Direct WebRTC Player:</strong> Uses the direct RTSP URL endpoint (/api/direct-webrtc)</li>
            </ul>
          </li>
          <li>Click "Start Stream" to begin the test</li>
          <li>The video should appear with minimal latency</li>
          <li>Check the status indicator for connection state</li>
          <li>If errors occur, they will be displayed above the video</li>
        </ol>

        <h4>Expected Results:</h4>
        <ul style={{ lineHeight: '1.6' }}>
          <li>Status should change from "Idle" → "Connecting..." → "Live"</li>
          <li>Video stream should display smoothly</li>
          <li>No error messages should appear</li>
          <li>Both test modes should work successfully</li>
        </ul>

        <h4>Troubleshooting:</h4>
        <ul style={{ lineHeight: '1.6' }}>
          <li>If connection fails, check network connectivity to ************</li>
          <li>Verify camera credentials (service:Krnl$001)</li>
          <li>Ensure backend server is running on port 8000</li>
          <li>Check browser console for additional error details</li>
        </ul>
      </div>
    </div>
  );
};

export default Camera172Test;
