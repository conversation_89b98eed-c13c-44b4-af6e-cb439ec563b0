#!/usr/bin/env python3
"""
Test script for camera IP ************ with RTSP URL rtsp://service:Krnl$001@************:554
This script tests the RTSP stream and displays video using OpenCV.
"""

import cv2
import json
import os
import sys
import time
import asyncio
import logging
from pathlib import Path

# Add backend directory to path to import RTSPVideoStreamTrack
backend_dir = Path(__file__).parent / "backend"
sys.path.append(str(backend_dir))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_camera_config():
    """Load camera configuration from JSON file"""
    config_path = backend_dir / "data" / "camera_configuration.json"
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load camera configuration: {e}")
        return None

def test_rtsp_with_opencv(rtsp_url, duration=30):
    """Test RTSP stream using OpenCV directly"""
    logger.info(f"Testing RTSP URL with OpenCV: {rtsp_url}")

    # Create VideoCapture object
    cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

    # Configure capture parameters
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)  # 10 second timeout
    cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)   # 5 second read timeout

    if not cap.isOpened():
        logger.error("Failed to open RTSP stream")
        return False

    logger.info("RTSP stream opened successfully")

    # Get stream properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)

    logger.info(f"Stream properties: {width}x{height} @ {fps} FPS")

    frame_count = 0
    start_time = time.time()

    try:
        while True:
            ret, frame = cap.read()

            if not ret:
                logger.warning("Failed to read frame")
                break

            frame_count += 1
            current_time = time.time()
            elapsed = current_time - start_time

            # Display frame info every 30 frames (roughly every second at 30fps)
            if frame_count % 30 == 0:
                logger.info(f"Frame {frame_count} received - Elapsed: {elapsed:.1f}s")

            # Skip GUI display in headless environment
            # Just check if we can read frames successfully

            # Break after duration or if we've read enough frames
            if elapsed > duration or frame_count >= 100:  # Stop after 100 frames or duration
                break

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Error during streaming: {e}")
    finally:
        cap.release()
        # Skip cv2.destroyAllWindows() in headless environment

        elapsed = time.time() - start_time
        logger.info(f"Test completed: {frame_count} frames in {elapsed:.1f}s ({frame_count/elapsed:.1f} FPS)")

    return frame_count > 0

async def test_rtsp_with_webrtc_track(rtsp_url):
    """Test RTSP stream using the RTSPVideoStreamTrack class"""
    try:
        from webrtc_stream import RTSPVideoStreamTrack
        logger.info(f"Testing RTSP URL with RTSPVideoStreamTrack: {rtsp_url}")

        # Create the video track
        video_track = RTSPVideoStreamTrack(rtsp_url)

        # Wait a bit for the track to initialize
        await asyncio.sleep(2)

        # Try to receive a few frames
        frame_count = 0
        for i in range(10):
            try:
                frame = await video_track.recv()
                frame_count += 1
                logger.info(f"Received WebRTC frame {frame_count}: {frame.width}x{frame.height}")
                await asyncio.sleep(0.1)  # Small delay between frames
            except Exception as e:
                logger.error(f"Error receiving frame {i+1}: {e}")
                break

        # Clean up
        if hasattr(video_track, 'stop'):
            video_track.stop()

        return frame_count > 0

    except ImportError as e:
        logger.error(f"Could not import RTSPVideoStreamTrack: {e}")
        return False
    except Exception as e:
        logger.error(f"Error testing with RTSPVideoStreamTrack: {e}")
        return False

def main():
    """Main test function"""
    logger.info("Starting camera test for IP ************")

    # Load camera configuration
    config = load_camera_config()
    if not config:
        logger.error("Failed to load camera configuration")
        return

    # Find the RTSP URL for IP ************
    target_ip = "************"
    rtsp_url = None

    for collection_name, cameras in config.items():
        if target_ip in cameras:
            rtsp_url = cameras[target_ip]
            logger.info(f"Found camera {target_ip} in collection '{collection_name}': {rtsp_url}")
            break

    if not rtsp_url:
        logger.error(f"Camera IP {target_ip} not found in configuration")
        return

    # Test 1: OpenCV direct test
    logger.info("=" * 50)
    logger.info("TEST 1: OpenCV Direct Test")
    logger.info("=" * 50)

    opencv_success = test_rtsp_with_opencv(rtsp_url, duration=10)

    # Test 2: WebRTC Track test
    logger.info("=" * 50)
    logger.info("TEST 2: WebRTC Track Test")
    logger.info("=" * 50)

    webrtc_success = asyncio.run(test_rtsp_with_webrtc_track(rtsp_url))

    # Summary
    logger.info("=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Camera IP: {target_ip}")
    logger.info(f"RTSP URL: {rtsp_url}")
    logger.info(f"OpenCV Test: {'PASSED' if opencv_success else 'FAILED'}")
    logger.info(f"WebRTC Test: {'PASSED' if webrtc_success else 'FAILED'}")

    if opencv_success and webrtc_success:
        logger.info("✅ All tests PASSED - Camera is working correctly!")
    elif opencv_success:
        logger.info("⚠️  OpenCV test passed but WebRTC test failed")
    elif webrtc_success:
        logger.info("⚠️  WebRTC test passed but OpenCV test failed")
    else:
        logger.info("❌ All tests FAILED - Check camera connection and credentials")

if __name__ == "__main__":
    main()
