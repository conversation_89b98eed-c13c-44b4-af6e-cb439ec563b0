from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
import json
import os
import subprocess
from pathlib import Path
import signal
import atexit
import shutil
import logging
import time
import re
import asyncio
import uuid
from routes import analytics, users, events, camera_rules, collections, webrtc, augment
from fastapi.responses import JSONResponse
from fastapi import status
import cv2
import asyncio
from aiortc import RTCPeerConnection, RTCSessionDescription, VideoStreamTrack
from av import VideoFrame

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Store active peer connections
pcs = set()

class RTSPVideoStreamTrack(VideoStreamTrack):
    def __init__(self, rtsp_url):
        super().__init__()
        self.rtsp_url = rtsp_url
        self.cap = cv2.VideoCapture(rtsp_url)

    async def recv(self):
        pts, time_base = await self.next_timestamp()
        ret, frame = self.cap.read()
        if not ret:
            await asyncio.sleep(1/30)
            return await self.recv()
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        new_frame = VideoFrame.from_ndarray(frame, format="rgb24")
        new_frame.pts = pts
        new_frame.time_base = time_base
        return new_frame

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Get local IP address for ICE configuration
def get_local_ip():
    try:
        # Create a socket to determine the outgoing IP address
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))  # Google's DNS server
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception as e:
        logger.warning(f"Could not determine local IP: {e}")
        return None

# Configure ICE servers and options
def get_rtc_configuration():
    ice_servers = [
        RTCIceServer("stun:stun.l.google.com:19302"),
        RTCIceServer("stun:global.stun.twilio.com:3478")
    ]

    # Get host override from environment or use detected local IP
    host_override = os.environ.get("ICE_HOST_OVERRIDE", get_local_ip())

    # Set the ICE_HOST_OVERRIDE environment variable if we have a valid IP
    if host_override:
        logger.info(f"Using ICE host override: {host_override}")
        os.environ["ICE_HOST_OVERRIDE"] = host_override

    # Return configuration with only supported parameters
    return RTCConfiguration(iceServers=ice_servers)

# Simple WebRTC implementation - no socket.io needed

# Get the backend directory
BACKEND_DIR = os.path.dirname(os.path.abspath(__file__))
CAMERA_JSON_PATH = os.path.join(BACKEND_DIR, "data/camera_configuration.json")
STREAM_OUTPUT_DIR = os.path.join(BACKEND_DIR, "streams")

# Log the paths for debugging
logger.debug(f"Backend directory: {BACKEND_DIR}")
logger.debug(f"Looking for camera config at: {CAMERA_JSON_PATH}")
logger.debug(f"File exists: {os.path.exists(CAMERA_JSON_PATH)}")

# Ensure streams directory exists
os.makedirs(STREAM_OUTPUT_DIR, exist_ok=True)

# Dictionary to store FFmpeg processes
ffmpeg_processes = {}

# 1. Ensure stream directory (simple)
def ensure_stream_directory():
    if not os.path.exists(STREAM_OUTPUT_DIR):
        os.makedirs(STREAM_OUTPUT_DIR)

# 2. Start FFmpeg stream (simple)
def start_ffmpeg_stream(rtsp_url, stream_name):
    output_dir = os.path.join(STREAM_OUTPUT_DIR, stream_name)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_path = os.path.join(output_dir, "stream.m3u8")
    output_path_win = output_path.replace('/', '\\')

    # Find ffmpeg path (keep your logic for this)
    # Get the workspace root directory (one level up from backend)
    workspace_root = os.path.abspath(os.path.join(BACKEND_DIR, ".."))
    possible_paths = [
        os.path.join(workspace_root, "ffmpeg", "bin", "ffmpeg.exe"),
        os.path.join(workspace_root, "ffmpeg.exe"),
        os.path.join(workspace_root, "ffmpeg-master-latest-win64-gpl-shared", "bin", "ffmpeg.exe"),
        "ffmpeg",
        "ffmpeg.exe",
        r"C:\\ffmpeg\\bin\\ffmpeg.exe"
    ]
    ffmpeg_path = None
    for path in possible_paths:
        try:
            if path in ["ffmpeg", "ffmpeg.exe"]:
                result = subprocess.run(["where", path], capture_output=True, text=True)
                if result.returncode == 0:
                    ffmpeg_path = path
                    break
            elif os.path.exists(path):
                ffmpeg_path = path
                break
        except Exception as e:
            pass
    if not ffmpeg_path:
        return {"error": "FFmpeg not found in any expected location. Please install FFmpeg."}

    command = [
        ffmpeg_path,
        "-i", rtsp_url,
        "-c:v", "copy",
        "-c:a", "aac",
        "-hls_time", "2",
        "-hls_list_size", "3",
        "-hls_flags", "delete_segments+temp_file",
        "-hls_allow_cache", "0",
        "-f", "hls",
        output_path_win
    ]
    logger.debug(f"Running FFmpeg command: {' '.join(command)}")
    try:
        # Redirect stdout and stderr to hide FFmpeg output
        process = subprocess.Popen(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        ffmpeg_processes[stream_name] = process

        # Return the proper URL for the frontend to use
        return f"http://localhost:8000/{stream_name}/stream.m3u8"
    except Exception as e:
        logger.error(f"Error starting FFmpeg: {str(e)}")
        return None

# 3. Cleanup streams (simple)
def cleanup_streams():
    for process in ffmpeg_processes.values():
        try:
            process.terminate()
        except:
            pass
    ffmpeg_processes.clear()
    if os.path.exists(STREAM_OUTPUT_DIR):
        shutil.rmtree(STREAM_OUTPUT_DIR)

atexit.register(cleanup_streams)

# Direct WebRTC offer endpoint
@app.post("/api/webrtc/direct-offer")
async def handle_direct_offer(request: Request):
    """
    Handle WebRTC offer from client with direct RTSP URL
    """
    try:
        body = await request.json()
        offer = RTCSessionDescription(sdp=body["sdp"], type=body["type"])
        rtsp_url = body["rtspUrl"]

        if not rtsp_url:
            return JSONResponse({"error": "RTSP URL is required"}, status_code=400)

        logger.info(f"Received direct offer for RTSP URL: {rtsp_url}")

        # Create a new RTCPeerConnection with our configuration
        pc = RTCPeerConnection(configuration=get_rtc_configuration())
        pc_id = str(uuid.uuid4())

        # Add cleanup callback
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"Connection state changed to: {pc.connectionState}")
            if pc.connectionState == "failed" or pc.connectionState == "closed":
                logger.info(f"Removing peer connection {pc_id}")

        # Log ICE connection state changes
        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"ICE connection state changed to: {pc.iceConnectionState}")
            if pc.iceConnectionState == "completed":
                logger.info("ICE completed")

        # Create a video track from the RTSP URL
        logger.info(f"Creating video track for RTSP URL: {rtsp_url}")
        video = RTSPVideoStreamTrack(rtsp_url)
        pc.addTrack(video)

        # Set remote description
        await pc.setRemoteDescription(offer)

        # Create answer
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)

        logger.info("Created answer, sending back to client")
        return JSONResponse({
            "sdp": pc.localDescription.sdp,
            "type": pc.localDescription.type
        })
    except Exception as e:
        logger.error(f"Error handling direct offer: {str(e)}")
        return JSONResponse({"error": str(e)}, status_code=500)

# Simple WebRTC offer endpoint (based on reference code)
@app.post("/offer")
async def offer(request: Request):
    """Handle WebRTC offer from client using camera configuration"""
    try:
        body = await request.json()
        offer = RTCSessionDescription(sdp=body["sdp"], type=body["type"])

        # Get camera info from request
        collection_name = body.get("collection_name")
        camera_ip = body.get("camera_ip")

        if not collection_name or not camera_ip:
            return JSONResponse({"error": "collection_name and camera_ip are required"}, status_code=400)

        # Read camera configuration
        if not os.path.exists(CAMERA_JSON_PATH):
            return JSONResponse({"error": "camera_configuration.json not found"}, status_code=404)

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)

        if collection_name not in camera_data:
            return JSONResponse({"error": "Collection not found"}, status_code=404)

        if camera_ip not in camera_data[collection_name]:
            return JSONResponse({"error": "Camera IP not found in collection"}, status_code=404)

        # Get the RTSP URL for this camera
        rtsp_url = camera_data[collection_name][camera_ip]

        logger.info(f"Creating WebRTC connection for {collection_name}/{camera_ip} -> {rtsp_url}")

        pc = RTCPeerConnection()
        pcs.add(pc)

        video = RTSPVideoStreamTrack(rtsp_url)
        pc.addTrack(video)

        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)

        return JSONResponse({
            "sdp": pc.localDescription.sdp,
            "type": pc.localDescription.type
        })

    except Exception as e:
        logger.error(f"Error handling offer: {str(e)}")
        return JSONResponse({"error": str(e)}, status_code=500)

@app.post("/api/direct-webrtc")
async def handle_direct_webrtc(request: Request):
    """
    Handle direct WebRTC streaming with RTSP URL provided directly.
    """
    try:
        body = await request.json()
        offer = RTCSessionDescription(sdp=body["sdp"], type=body["type"])

        # Get RTSP URL directly from request
        rtsp_url = body.get("rtsp_url")

        if not rtsp_url:
            return JSONResponse({
                "success": False,
                "error": "rtsp_url is required"
            }, status_code=400)

        logger.info(f"Creating direct WebRTC connection for RTSP URL: {rtsp_url}")

        pc = RTCPeerConnection()
        pcs.add(pc)

        video = RTSPVideoStreamTrack(rtsp_url)
        pc.addTrack(video)

        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)

        return JSONResponse({
            "sdp": pc.localDescription.sdp,
            "type": pc.localDescription.type
        })

    except Exception as e:
        logger.error(f"Error handling direct WebRTC: {str(e)}")
        return JSONResponse({
            "success": False,
            "error": str(e)
        }, status_code=500)

# Legacy WebRTC endpoints (kept for compatibility but now use simple /offer endpoint)

# Add a new endpoint to restart a specific stream
@app.get("/restart-stream/{collection_name}/{camera_ip}")
async def restart_stream(collection_name: str, camera_ip: str):
    try:
        logger.debug(f"Restarting stream for camera {camera_ip} in collection {collection_name}")

        if not os.path.exists(CAMERA_JSON_PATH):
            logger.error(f"Config file not found at: {CAMERA_JSON_PATH}")
            return {"error": "camera_configuration.json not found"}

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)

        if collection_name not in camera_data:
            logger.error(f"Collection '{collection_name}' not found in camera data")
            return {"error": "Collection not found"}

        if camera_ip not in camera_data[collection_name]:
            logger.error(f"Camera IP '{camera_ip}' not found in collection '{collection_name}'")
            return {"error": "Camera IP not found in collection"}

        # Get the RTSP URL for this camera
        rtsp_url = camera_data[collection_name][camera_ip]

        # Create a stream name
        # Use a URL-safe stream name (replace all spaces with underscores)
        # This ensures consistent handling of collection names with numbers
        safe_collection_name = re.sub(r'\s+', '_', collection_name)
        stream_name = f"{safe_collection_name}_{camera_ip}"

        # Kill any existing FFmpeg process for this stream
        if stream_name in ffmpeg_processes:
            try:
                logger.debug(f"Terminating existing FFmpeg process for {stream_name}")
                ffmpeg_processes[stream_name].terminate()
                try:
                    ffmpeg_processes[stream_name].wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning(f"Process for {stream_name} did not terminate, killing it")
                    ffmpeg_processes[stream_name].kill()
                del ffmpeg_processes[stream_name]
            except Exception as e:
                logger.error(f"Error terminating FFmpeg process: {str(e)}")

        # Start a new stream
        result = start_ffmpeg_stream(rtsp_url, stream_name)

        if isinstance(result, dict) and "error" in result:
            return {"error": result["error"]}

        return {"status": "success", "stream_url": result}

    except Exception as e:
        logger.error(f"Error restarting stream: {str(e)}")
        return {"error": str(e)}

@app.get("/start-streams/{collection_name}")
async def start_camera_streams(collection_name: str):
    try:
        logger.debug(f"Starting streams for collection: {collection_name}")
        logger.debug(f"Looking for config file at: {CAMERA_JSON_PATH}")

        if not os.path.exists(CAMERA_JSON_PATH):
            logger.error(f"Config file not found at: {CAMERA_JSON_PATH}")
            return {"error": "camera_configuration.json not found"}

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)
            logger.debug(f"Loaded camera data: {json.dumps(camera_data, indent=2)}")
            logger.debug(f"Available collections: {list(camera_data.keys())}")

        if collection_name not in camera_data:
            logger.error(f"Collection '{collection_name}' not found in camera data")
            return {"error": "Collection not found"}

        ensure_stream_directory()

        # Convert RTSP streams to HLS
        stream_urls = []
        errors = []

        for camera_ip, rtsp_url in camera_data[collection_name].items():
            # Use a URL-safe stream name (replace all spaces with underscores)
            # This ensures consistent handling of collection names with numbers
            safe_collection_name = re.sub(r'\s+', '_', collection_name)
            stream_name = f"{safe_collection_name}_{camera_ip}"
            logger.debug(f"Starting stream for {stream_name} with URL: {rtsp_url}")

            # Kill any existing FFmpeg process for this stream
            if stream_name in ffmpeg_processes:
                try:
                    logger.debug(f"Terminating existing FFmpeg process for {stream_name}")
                    ffmpeg_processes[stream_name].terminate()
                    try:
                        ffmpeg_processes[stream_name].wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Process for {stream_name} did not terminate, killing it")
                        ffmpeg_processes[stream_name].kill()
                    del ffmpeg_processes[stream_name]
                except Exception as e:
                    logger.error(f"Error terminating FFmpeg process: {str(e)}")

            result = start_ffmpeg_stream(rtsp_url, stream_name)

            # Check if result is a dictionary with an error
            if isinstance(result, dict) and "error" in result:
                errors.append(f"Camera {camera_ip}: {result['error']}")
                continue

            # Store the stream URL in our list
            if result:
                stream_urls.append(result)

        response = {
            "collection": collection_name,
            "streams": stream_urls,
            "count": len(stream_urls)
        }

        if errors:
            response["warnings"] = errors

        return response
    except Exception as e:
        logger.error(f"Error starting streams: {str(e)}")
        return {"error": str(e)}

@app.get("/collections")
async def get_collections():
    try:
        if not os.path.exists(CAMERA_JSON_PATH):
            return {"error": "camera_configuration.json not found"}

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)

        collections = list(camera_data.keys())
        return {"collections": collections}
    except Exception as e:
        return {"error": str(e)}

@app.get("/cameras")
async def get_cameras():
    """Get all cameras from the configuration file"""
    try:
        if not os.path.exists(CAMERA_JSON_PATH):
            return {"error": "camera_configuration.json not found"}

        with open(CAMERA_JSON_PATH, "r") as f:
            camera_data = json.load(f)

        return {"cameras": camera_data}
    except Exception as e:
        logger.error(f"Error getting cameras: {str(e)}")
        return {"error": str(e)}

# Custom endpoint to serve m3u8 files with no-cache headers
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from fastapi import HTTPException

# Custom endpoint to serve m3u8 files with proper headers
@app.get("/{stream_name}/stream.m3u8")
async def serve_m3u8(stream_name: str):
    file_path = os.path.join(STREAM_OUTPUT_DIR, stream_name, "stream.m3u8")

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Stream not found")

    # Just serve the actual m3u8 file that FFmpeg is writing to
    return FileResponse(
        file_path,
        media_type="application/vnd.apple.mpegurl",
        headers={
            "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

# Custom endpoint to serve ts files with proper headers
@app.get("/{stream_name}/stream{segment_number}.ts")
async def serve_ts_segment(stream_name: str, segment_number: int):
    file_path = os.path.join(STREAM_OUTPUT_DIR, stream_name, f"stream{segment_number}.ts")

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Stream segment not found")

    return FileResponse(
        file_path,
        media_type="video/mp2t",
        headers={
            "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
            "Pragma": "no-cache",
            "Expires": "0"
        }
    )

# Middleware to add no-cache headers to all stream responses
class NoCacheMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        response = await call_next(request)
        if request.url.path.startswith("/streams/"):
            response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        return response

app.add_middleware(NoCacheMiddleware)
app.mount("/streams", StaticFiles(directory=STREAM_OUTPUT_DIR), name="streams")

# Include the routers
app.include_router(analytics.router)
app.include_router(users.router)
app.include_router(events.router)
app.include_router(camera_rules.router)
app.include_router(collections.router)
app.include_router(augment.router)
app.include_router(webrtc.router)

@app.delete("/collections/{collection_name}")
async def delete_collection(collection_name: str):
    """
    Delete a collection from camera_configuration.json.
    - Backs up the old file to camera_configuration.json.bak
    - Returns 200 OK on success, 404 if not found, 500 on error
    """
    try:
        if not os.path.exists(CAMERA_JSON_PATH):
            logger.error(f"Config file not found at: {CAMERA_JSON_PATH}")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content={"error": "camera_configuration.json not found"})

        # Read current config
        with open(CAMERA_JSON_PATH, "r") as f:
            try:
                camera_data = json.load(f)
            except Exception as e:
                logger.error(f"Error parsing camera_configuration.json: {e}")
                return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"error": "Malformed camera_configuration.json"})

        if collection_name not in camera_data:
            logger.warning(f"Collection '{collection_name}' not found in configuration")
            return JSONResponse(status_code=status.HTTP_404_NOT_FOUND, content={"error": f"Collection '{collection_name}' not found in configuration"})

        # Remove the collection
        del camera_data[collection_name]

        # Backup the old file
        backup_path = CAMERA_JSON_PATH + ".bak"
        try:
            shutil.copy2(CAMERA_JSON_PATH, backup_path)
        except Exception as e:
            logger.error(f"Failed to backup camera_configuration.json: {e}")
            return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"error": "Failed to backup configuration file"})

        # Write to a temp file first
        temp_path = CAMERA_JSON_PATH + ".tmp"
        try:
            with open(temp_path, "w") as f:
                json.dump(camera_data, f, indent=2)
            os.replace(temp_path, CAMERA_JSON_PATH)
        except Exception as e:
            logger.error(f"Failed to write updated configuration: {e}")
            return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"error": "Failed to write updated configuration"})

        logger.info(f"Collection '{collection_name}' deleted successfully.")
        return {"message": f"Collection '{collection_name}' deleted successfully."}
    except Exception as e:
        logger.error(f"Unexpected error deleting collection: {e}")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"error": str(e)})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)